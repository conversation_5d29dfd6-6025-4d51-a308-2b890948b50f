"use client";

import { useState, useMemo, memo, useCallback } from "react";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  CheckCircle,
  XCircle,
  AlertTriangle,
  Info,
  Loader2,
} from "lucide-react";
import { cn } from "@/lib/utils";
import {
  useBatchValidation,
  useBatchValidationRules,
} from "@/hooks/use-batch-validation";
import { BatchHistoryDialog } from "@/components/batch-validation/batch-history-dialog";

interface BatchValidationProps {
  value: string;
  onChange: (value: string) => void;
  productId?: string;
  supplierId?: string;
  expiryDate?: Date;
  manufacturingDate?: Date;
  label?: string;
  placeholder?: string;
  disabled?: boolean;
  required?: boolean;
  showHistory?: boolean;
  showFormatGuide?: boolean;
  className?: string;
  error?: string;
  onValidationRequest?: () => void;
}

const AdvancedBatchNumberInputComponent = function AdvancedBatchNumberInput({
  value,
  onChange,
  productId,
  supplierId,
  expiryDate,
  manufacturingDate,
  placeholder = "Masukkan nomor batch...",
  disabled = false,
  className,
  error,
}: BatchValidationProps) {
  const [showFormatDialog, setShowFormatDialog] = useState(false);
  const [showHistoryDialog, setShowHistoryDialog] = useState(false);

  // Memoize validation options to prevent unnecessary hook re-runs
  const validationOptions = useMemo(() => ({
    productId,
    supplierId,
    expiryDate,
    manufacturingDate,
    debounceMs: 500, // Slightly increase debounce for better performance
    enableRealTimeValidation: !!value && !!productId, // Only enable when both values exist
  }), [productId, supplierId, expiryDate, manufacturingDate, value]);

  // Use the batch validation hook with enhanced loading states
  const {
    validationResult,
    isLoading: isValidating,
    hasWarnings,
    hasErrors,
    realTimeError,
    getFormatSuggestion
  } = useBatchValidation(value, validationOptions);

  // Get validation rules for format guide - memoized
  const { data: validationRules } = useBatchValidationRules();

  // Get validation icon and color with enhanced states - memoized to prevent re-renders
  const validationIcon = useMemo(() => {
    if (isValidating) {
      return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />;
    }

    // Show error state for network/API errors
    if (realTimeError) {
      return <XCircle className="h-4 w-4 text-red-500" />;
    }

    if (!validationResult) {
      // Show a subtle indicator when no validation has occurred yet
      if (value && productId) {
        return <Info className="h-4 w-4 text-muted-foreground/50" />;
      }
      // Show warning if batch number exists but no product selected
      if (value && !productId) {
        return <AlertTriangle className="h-4 w-4 text-yellow-500/70" />;
      }
      return null;
    }

    switch (validationResult.level) {
      case "success":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "warning":
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case "error":
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return null;
    }
  }, [isValidating, realTimeError, validationResult, value, productId]);

  const validationColor = useMemo(() => {
    if (isValidating) {
      return "border-blue-300 focus:border-blue-500 bg-blue-50/30";
    }

    // Show error styling for network/API errors
    if (realTimeError) {
      return "border-red-500 focus:border-red-500 bg-red-50/30";
    }

    if (!validationResult) {
      // Show warning styling if batch number exists but no product selected
      if (value && !productId) {
        return "border-yellow-400 focus:border-yellow-500 bg-yellow-50/30";
      }
      return "";
    }

    switch (validationResult.level) {
      case "success":
        return "border-green-500 focus:border-green-500 bg-green-50/30";
      case "warning":
        return "border-yellow-500 focus:border-yellow-500 bg-yellow-50/30";
      case "error":
        return "border-red-500 focus:border-red-500 bg-red-50/30";
      default:
        return "";
    }
  }, [isValidating, realTimeError, validationResult, value, productId]);

  const validationMessage = useMemo(() => {
    if (isValidating) {
      return "Memvalidasi nomor batch...";
    }

    // Show error message for network/API errors
    if (realTimeError) {
      if (realTimeError instanceof Error) {
        return realTimeError.message;
      }
      return "Terjadi kesalahan saat validasi";
    }

    if (!validationResult) {
      if (value && !productId) {
        return "Pilih produk untuk validasi batch";
      }
      // Show format suggestion if available
      if (value) {
        const formatSuggestion = getFormatSuggestion(value);
        if (formatSuggestion) {
          return `Format terdeteksi: ${formatSuggestion}`;
        }
      }
      return null;
    }

    return validationResult.message;
  }, [isValidating, realTimeError, validationResult, value, productId, getFormatSuggestion]);

  // Memoized event handlers to prevent unnecessary re-renders
  const handleShowHistory = useCallback(async () => {
    if (!value) return;
    setShowHistoryDialog(true);
    // History dialog content will be loaded when opened
  }, [value]);

  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    onChange(e.target.value);
  }, [onChange]);

  return (
    <div className={cn("space-y-2", className)}>
      {/* Label with help icons */}
      {/* <div className="flex items-center gap-2">
        <Label
          htmlFor="batch-number"
          className={required ? "after:content-['*'] after:text-red-500" : ""}
        >
          {label}
        </Label>

        {showFormatGuide && (
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="h-5 w-5 p-0"
                onClick={() => setShowFormatDialog(true)}
              >
                <HelpCircle className="h-3 w-3" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Lihat panduan format batch number</p>
            </TooltipContent>
          </Tooltip>
        )}

        {showHistory && value && (
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="h-5 w-5 p-0"
                onClick={handleShowHistory}
              >
                <History className="h-3 w-3" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Lihat riwayat batch number</p>
            </TooltipContent>
          </Tooltip>
        )}

        {onValidationRequest && value && productId && (
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="h-5 w-5 p-0"
                onClick={onValidationRequest}
              >
                <CheckCircle className="h-3 w-3" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Validasi batch number lengkap</p>
            </TooltipContent>
          </Tooltip>
        )}
      </div> */}

      {/* Input with validation indicator */}
      <div className="relative">
        <Input
          id="batch-number"
          type="text"
          value={value}
          onChange={handleInputChange}
          placeholder={placeholder}
          disabled={disabled || isValidating}
          className={cn(
            "w-full transition-all duration-200",
            validationColor,
            error && "border-red-500 focus:border-red-500 bg-red-50/30",
            isValidating && "cursor-wait",
          )}
        />

        {/* Validation icon with enhanced tooltip */}
        {validationIcon && (
          <Tooltip>
            <TooltipTrigger asChild>
              <div className="absolute right-2 top-1/2 -translate-y-1/2 cursor-help">
                {validationIcon}
              </div>
            </TooltipTrigger>
            <TooltipContent side="top" className="max-w-xs">
              <div className="space-y-1">
                <span className="font-medium">{validationMessage}</span>
                {isValidating && (
                  <div className="text-xs text-muted-foreground">
                    Validasi real-time dengan debounce 500ms
                  </div>
                )}
                {validationResult?.level === "success" && (
                  <div className="text-xs text-green-600">
                    ✓ Batch number valid dan dapat digunakan
                  </div>
                )}
                {validationResult?.level === "warning" && (
                  <div className="text-xs text-yellow-600">
                    ⚠ Perhatikan peringatan sebelum melanjutkan
                  </div>
                )}
              </div>
            </TooltipContent>
          </Tooltip>
        )}
      </div>

      {/* Enhanced validation message with better styling */}
      {(validationMessage || error) && (
        <div className={cn(
          "flex items-start gap-2 text-xs mt-2 p-2 rounded-md transition-all duration-200",
          validationResult?.level === "error" || error ?
            "text-red-700 bg-red-50 border border-red-200" :
            validationResult?.level === "warning" ?
              "text-yellow-700 bg-yellow-50 border border-yellow-200" :
              validationResult?.level === "success" ?
                "text-green-700 bg-green-50 border border-green-200" :
                isValidating ?
                  "text-blue-700 bg-blue-50 border border-blue-200" :
                  "text-muted-foreground bg-muted/30 border border-muted"
        )}>
          <div className="flex-shrink-0 mt-0.5">
            {validationResult?.level === "error" || error ?
              <XCircle className="h-3 w-3" /> :
              validationResult?.level === "warning" ?
                <AlertTriangle className="h-3 w-3" /> :
                validationResult?.level === "success" ?
                  <CheckCircle className="h-3 w-3" /> :
                  isValidating ?
                    <Loader2 className="h-3 w-3 animate-spin" /> :
                    <Info className="h-3 w-3" />}
          </div>
          <div className="flex-1 min-w-0">
            <span className="font-medium">{error || validationMessage}</span>
            {/* Additional context for validation states */}
            {validationResult?.level === "success" && !error && (
              <div className="text-xs text-green-600 mt-1 opacity-75">
                Batch number telah divalidasi dan siap digunakan
              </div>
            )}
            {validationResult?.level === "warning" && !error && (
              <div className="text-xs text-yellow-600 mt-1 opacity-75">
                Periksa kembali informasi batch sebelum melanjutkan
              </div>
            )}
            {realTimeError && (
              <div className="text-xs text-red-600 mt-1 opacity-75">
                Coba lagi dalam beberapa saat atau periksa koneksi internet
              </div>
            )}
          </div>
        </div>
      )}

      {/* Format Guide Dialog */}
      <Dialog open={showFormatDialog} onOpenChange={setShowFormatDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Panduan Format Nomor Batch</DialogTitle>
            <DialogDescription>
              Pilih format yang sesuai dengan jenis produk dan standar yang
              berlaku
            </DialogDescription>
          </DialogHeader>

          <ScrollArea className="max-h-96">
            <div className="space-y-4">
              {validationRules?.formats ? (
                Object.entries(validationRules.formats).map(
                  ([key, pattern]: [string, any]) => (
                    <div key={key} className="border rounded-lg p-4">
                      <div className="flex items-center gap-2 mb-2">
                        <Badge variant="outline">{key}</Badge>
                      </div>
                      <p className="text-sm text-muted-foreground mb-2">
                        {pattern.description}
                      </p>
                      <div className="bg-muted p-2 rounded text-sm font-mono">
                        Contoh: {pattern.example}
                      </div>
                    </div>
                  ),
                )
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
                  <p>Memuat aturan validasi...</p>
                </div>
              )}
            </div>
          </ScrollArea>
        </DialogContent>
      </Dialog>

      {/* Batch History Dialog */}
      <BatchHistoryDialog
        batchNumber={value}
        open={showHistoryDialog}
        onOpenChange={setShowHistoryDialog}
      />
    </div>
  );
};

// Export memoized component to prevent unnecessary re-renders
export const AdvancedBatchNumberInput = memo(AdvancedBatchNumberInputComponent);

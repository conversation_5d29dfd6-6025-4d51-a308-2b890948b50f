"use client";

import { UseFormReturn, useWatch } from "react-hook-form";
import { format } from "date-fns";
import { id } from "date-fns/locale";
import { CalendarIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import { useState, memo, useCallback, useMemo } from "react";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { SupplierSelector } from "@/components/ui/supplier-selector";
import { PurchaseOrder } from "@/types/purchase-order";
import { formatCurrency } from "@/lib/utils";
import { GoodsReceiptFormValues, NO_PURCHASE_ORDER } from "./goods-receipt-schemas";

interface FormHeaderSectionProps {
  form: UseFormReturn<GoodsReceiptFormValues>;
  purchaseOrdersResponse?: { data: PurchaseOrder[] };
  onPurchaseOrderChange: (purchaseOrderId: string) => void;
  onClearPurchaseOrder: () => void;
}

const FormHeaderSectionComponent = function FormHeaderSection({
  form,
  purchaseOrdersResponse,
  onPurchaseOrderChange,
  onClearPurchaseOrder,
}: FormHeaderSectionProps) {
  const [alertDialog, setAlertDialog] = useState<{
    isOpen: boolean;
    type: 'supplier-change' | 'po-change';
    title: string;
    description: string;
    onConfirm: () => void;
  }>({
    isOpen: false,
    type: 'supplier-change',
    title: '',
    description: '',
    onConfirm: () => { },
  });

  // Optimized watching - use useWatch instead of form.watch
  const watchedFields = useWatch({
    control: form.control,
    name: ["purchaseOrderId", "supplierId"],
  });

  // Memoized current values
  const { currentPOId, currentSupplierId } = useMemo(() => {
    const [poId, supplierId] = watchedFields || [];
    return {
      currentPOId: poId || "",
      currentSupplierId: supplierId || "",
    };
  }, [watchedFields]);

  // Find current PO details - memoized
  const currentPO = useMemo(() => {
    return purchaseOrdersResponse?.data?.find(po => po.id === currentPOId);
  }, [purchaseOrdersResponse?.data, currentPOId]);

  // Memoized validation functions
  const handleSupplierChange = useCallback((newSupplierId: string) => {
    // If there's a PO selected and it belongs to a different supplier
    if (currentPOId && currentPOId !== NO_PURCHASE_ORDER && currentPO && currentPO.supplierId !== newSupplierId) {
      setAlertDialog({
        isOpen: true,
        type: 'supplier-change',
        title: 'Konfirmasi Perubahan Supplier',
        description: `Purchase Order "${currentPO.orderNumber}" milik supplier lain. Mengubah supplier akan menghapus PO yang dipilih. Lanjutkan?`,
        onConfirm: () => {
          // Clear PO and change supplier
          onClearPurchaseOrder();
          form.setValue("supplierId", newSupplierId);
          setAlertDialog(prev => ({ ...prev, isOpen: false }));
        },
      });
    } else {
      // Safe to change supplier
      form.setValue("supplierId", newSupplierId);
    }
  }, [currentPOId, currentPO, onClearPurchaseOrder, form]);

  const handlePOChange = useCallback((newPOId: string) => {
    if (newPOId === NO_PURCHASE_ORDER || !newPOId) {
      // Clearing PO or selecting "Tanpa PO" - always allowed
      const actualValue = newPOId === NO_PURCHASE_ORDER ? "" : newPOId;
      form.setValue("purchaseOrderId", actualValue);
      onPurchaseOrderChange(actualValue);
      return;
    }

    const selectedPO = purchaseOrdersResponse?.data?.find(po => po.id === newPOId);

    // If there's a supplier selected and PO belongs to different supplier
    if (currentSupplierId && selectedPO && selectedPO.supplierId !== currentSupplierId) {
      setAlertDialog({
        isOpen: true,
        type: 'po-change',
        title: 'Konfirmasi Perubahan Purchase Order',
        description: `Purchase Order "${selectedPO.orderNumber}" milik supplier "${selectedPO.supplier.name}". Memilih PO ini akan mengubah supplier. Lanjutkan?`,
        onConfirm: () => {
          // Change both PO and supplier
          form.setValue("purchaseOrderId", newPOId);
          form.setValue("supplierId", selectedPO.supplierId);
          onPurchaseOrderChange(newPOId);
          setAlertDialog(prev => ({ ...prev, isOpen: false }));
        },
      });
    } else {
      // Safe to change PO
      form.setValue("purchaseOrderId", newPOId);
      onPurchaseOrderChange(newPOId);

      // If no supplier selected, auto-select the PO's supplier
      if (!currentSupplierId && selectedPO) {
        form.setValue("supplierId", selectedPO.supplierId);
      }
    }
  }, [currentSupplierId, purchaseOrdersResponse?.data, form, onPurchaseOrderChange]);
  return (
    <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 pb-4 md:p-4">
      {/* Purchase Order Selection */}
      <div className="overflow-hidden">
        <FormField
          control={form.control}
          name="purchaseOrderId"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-sm font-medium flex items-center gap-2">
                Purchase Order
                {field.value && field.value !== NO_PURCHASE_ORDER && (
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={onClearPurchaseOrder}
                    className="h-5 px-2 text-xs text-red-600 hover:bg-red-50"
                  >
                    Hapus PO
                  </Button>
                )}
              </FormLabel>
              <Select
                value={field.value}
                onValueChange={handlePOChange}
              >
                <FormControl>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Pilih PO..." />
                  </SelectTrigger>
                </FormControl>
                <SelectContent className="max-h-[200px]">
                  <SelectItem value={NO_PURCHASE_ORDER}>Tanpa PO</SelectItem>
                  {purchaseOrdersResponse?.data
                    ?.filter(
                      (po) => {
                        // Filter by status
                        const validStatus = po.status === "APPROVED" ||
                          po.status === "ORDERED" ||
                          po.status === "PARTIALLY_RECEIVED";

                        // Filter by supplier if supplier is selected
                        const selectedSupplierId = form.watch("supplierId");
                        const matchesSupplier = !selectedSupplierId || po.supplierId === selectedSupplierId;

                        return validStatus && matchesSupplier;
                      }
                    )
                    ?.map((po) => (
                      <SelectItem key={po.id} value={po.id}>
                        <div className="text-xs">
                          <div className="font-medium">{po.orderNumber}</div>
                          <div className="text-muted-foreground">
                            {formatCurrency(po.totalAmount)} • {po.items.length} item
                          </div>
                        </div>
                      </SelectItem>
                    ))}
                </SelectContent>
              </Select>
              <FormMessage className="text-xs" />
            </FormItem>
          )}
        />
      </div>

      {/* Supplier Selection */}
      <div className="overflow-hidden">
        <FormField
          control={form.control}
          name="supplierId"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-sm font-medium">Supplier</FormLabel>
              <FormControl>
                <SupplierSelector
                  value={field.value}
                  onValueChange={handleSupplierChange}
                  placeholder="Pilih supplier..."
                  className="w-full max-w-full"
                />
              </FormControl>
              <FormMessage className="text-xs" />
            </FormItem>
          )}
        />
      </div>

      {/* Receipt Date */}
      <div className="overflow-hidden">
        <FormField
          control={form.control}
          name="receiptDate"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-sm font-medium">Tanggal Terima</FormLabel>
              <Popover>
                <PopoverTrigger asChild>
                  <FormControl>
                    <Button
                      variant="outline"
                      className={cn(
                        "h-9 w-full pl-3 text-left font-normal",
                        !field.value && "text-muted-foreground",
                      )}
                    >
                      {field.value ? (
                        format(new Date(field.value), "dd MMM yyyy", {
                          locale: id,
                        })
                      ) : (
                        <span>Pilih tanggal</span>
                      )}
                      <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                    </Button>
                  </FormControl>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <CalendarComponent
                    mode="single"
                    selected={field.value ? new Date(field.value) : undefined}
                    onSelect={(date) => {
                      field.onChange(date ? format(date, "yyyy-MM-dd") : "");
                    }}
                    disabled={(date) =>
                      date > new Date() || date < new Date("1900-01-01")
                    }
                    autoFocus
                  />
                </PopoverContent>
              </Popover>
              <FormMessage className="text-xs" />
            </FormItem>
          )}
        />
      </div>

      {/* Invoice Number */}
      <div className="overflow-hidden">
        <FormField
          control={form.control}
          name="invoiceNumber"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-sm font-medium">No. Invoice</FormLabel>
              <FormControl>
                <Input
                  {...field}
                  placeholder="Masukkan nomor invoice..."
                  className="h-9"
                />
              </FormControl>
              <FormMessage className="text-xs" />
            </FormItem>
          )}
        />
      </div>

      {/* Delivery Note */}
      <div className="overflow-hidden">
        <FormField
          control={form.control}
          name="deliveryNote"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-sm font-medium">Surat Jalan</FormLabel>
              <FormControl>
                <Input
                  {...field}
                  placeholder="Masukkan nomor surat jalan..."
                  className="h-9"
                />
              </FormControl>
              <FormMessage className="text-xs" />
            </FormItem>
          )}
        />
      </div>

      {/* Delivered By */}
      <div className="overflow-hidden">
        <FormField
          control={form.control}
          name="deliveredBy"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-sm font-medium">Pengirim</FormLabel>
              <FormControl>
                <Input
                  {...field}
                  placeholder="Nama pengirim..."
                  className="h-9"
                />
              </FormControl>
              <FormMessage className="text-xs" />
            </FormItem>
          )}
        />
      </div>

      {/* Received By */}
      <div className="overflow-hidden">
        <FormField
          control={form.control}
          name="receivedBy"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-sm font-medium">Penerima</FormLabel>
              <FormControl>
                <Input
                  {...field}
                  placeholder="Nama penerima..."
                  className="h-9"
                />
              </FormControl>
              <FormMessage className="text-xs" />
            </FormItem>
          )}
        />
      </div>

      {/* Alert Dialog for validation */}
      <AlertDialog open={alertDialog.isOpen} onOpenChange={(open) => setAlertDialog(prev => ({ ...prev, isOpen: open }))}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{alertDialog.title}</AlertDialogTitle>
            <AlertDialogDescription>
              {alertDialog.description}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Batal</AlertDialogCancel>
            <AlertDialogAction onClick={alertDialog.onConfirm}>
              Lanjutkan
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}

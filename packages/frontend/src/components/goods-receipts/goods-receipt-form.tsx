"use client";

import { useState, useEffect, use<PERSON>allback } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useImmer } from "use-immer";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

import { Form } from "@/components/ui/form";
import { BatchValidationSummary } from "@/components/batch-validation/batch-validation-summary";
import { BatchValidationDialog } from "@/components/batch-validation/batch-validation-dialog";
import { SimpleSubstitutionDialog } from "./simple-substitution-dialog";
import {
  GoodsReceiptFormData,
  CreateGoodsReceiptDto,
} from "@/types/goods-receipt";
import { PurchaseOrder, PurchaseOrderStatus } from "@/types/purchase-order";
import { usePurchaseOrders } from "@/hooks/usePurchaseOrders";
import { formatCurrency } from "@/lib/utils";
import { toast } from "sonner";

// Form sections
import {
  goodsReceiptFormSchema,
  GoodsReceiptFormValues,
  FormHeaderSection,
  ItemsSection,
  DetailsSection,
} from "./form-sections";

interface GoodsReceiptFormProps {
  initialData?: Partial<GoodsReceiptFormData>;
  onSubmit: (data: CreateGoodsReceiptDto) => Promise<void>;
  isSubmitting?: boolean;
  mode?: "create" | "edit";
}

export function GoodsReceiptForm({
  initialData,
  onSubmit,
  isSubmitting = false,
  mode = "create",
}: GoodsReceiptFormProps) {
  const [selectedPurchaseOrder, setSelectedPurchaseOrder] =
    useState<PurchaseOrder | null>(null);
  const [totalAmount, setTotalAmount] = useImmer(0);
  const [isPOModalOpen, setIsPOModalOpen] = useState(false);
  const [activeTab, setActiveTab] = useState("items");
  const [substitutionDialog, setSubstitutionDialog] = useState<{
    isOpen: boolean;
    itemIndex: number;
    originalProductId: string;
    originalProductName: string;
  }>({
    isOpen: false,
    itemIndex: -1,
    originalProductId: "",
    originalProductName: "",
  });
  const [batchValidationDialog, setBatchValidationDialog] = useState({
    isOpen: false,
    itemIndex: -1,
    batchNumber: "",
    productId: "",
    productName: "",
  });
  const [batchValidationResults, setBatchValidationResults] = useImmer<
    Record<string, any>
  >({});

  const { data: purchaseOrdersResponse } = usePurchaseOrders({
    supplierId: initialData?.supplierId,
    status: PurchaseOrderStatus.APPROVED,
    limit: 100,
  });

  const form = useForm<GoodsReceiptFormValues>({
    resolver: zodResolver(goodsReceiptFormSchema),
    defaultValues: {
      purchaseOrderId: initialData?.purchaseOrderId || "",
      supplierId: initialData?.supplierId || "",
      receiptDate:
        initialData?.receiptDate || new Date().toISOString().split("T")[0],
      deliveryDate: initialData?.deliveryDate || "",
      invoiceNumber: initialData?.invoiceNumber || "",
      deliveryNote: initialData?.deliveryNote || "",
      deliveredBy: initialData?.deliveredBy || "",
      receivedBy: initialData?.receivedBy || "",
      deliveryCondition: initialData?.deliveryCondition || "",

      notes: initialData?.notes || "",
      internalNotes: initialData?.internalNotes || "",
      items: initialData?.items || [],
    },
  });

  // Watch items for total calculation
  useEffect(() => {
    const subscription = form.watch((value, { name }) => {
      if (name?.startsWith("items")) {
        const items = value.items || [];
        const total = items.reduce((sum, item) => {
          const qty = item?.quantityReceived || 0;
          const price = item?.unitPrice || 0;
          return sum + qty * price;
        }, 0);
        setTotalAmount(total);
      }
    });
    return () => subscription.unsubscribe();
  }, [form, setTotalAmount]);

  const clearPurchaseOrderSelection = useCallback(() => {
    setSelectedPurchaseOrder(null);
    form.setValue("purchaseOrderId", "");
    form.setValue("supplierId", "");
    const currentNotes = form.getValues("notes");
    if (currentNotes && currentNotes.includes("Purchase Order:")) {
      form.setValue("notes", "");
    }
    form.setValue("deliveryDate", "");
    form.setValue("deliveredBy", "");
    toast.info("Purchase Order selection telah dihapus");
  }, [form]);

  const handlePurchaseOrderChange = useCallback(
    (purchaseOrderId: string) => {
      if (!purchaseOrderId) {
        setSelectedPurchaseOrder(null);
        return;
      }

      const purchaseOrder = purchaseOrdersResponse?.data.find(
        (po) => po.id === purchaseOrderId,
      );
      if (purchaseOrder) {
        setSelectedPurchaseOrder(purchaseOrder);
        form.setValue("supplierId", purchaseOrder.supplierId);

        if (purchaseOrder.expectedDelivery) {
          form.setValue(
            "deliveryDate",
            new Date(purchaseOrder.expectedDelivery)
              .toISOString()
              .split("T")[0],
          );
        }

        if (purchaseOrder.deliveryAddress) {
          form.setValue("deliveredBy", purchaseOrder.deliveryContact || "");
        }

        if (purchaseOrder.deliveryNotes) {
          form.setValue(
            "notes",
            `PO: ${purchaseOrder.orderNumber}\n${purchaseOrder.deliveryNotes}`,
          );
        } else {
          form.setValue(
            "notes",
            `Penerimaan untuk Purchase Order: ${purchaseOrder.orderNumber}`,
          );
        }

        form.setValue("items", []);

        // Add PO items
        const newItems = purchaseOrder.items.map((poItem) => {
          const remainingQuantity =
            poItem.quantityOrdered - poItem.quantityReceived;
          return {
            productId: poItem.productId,
            unitId: poItem.unitId,
            quantityOrdered: poItem.quantityOrdered,
            quantityReceived: remainingQuantity > 0 ? remainingQuantity : 0,
            quantityAccepted: 0,
            quantityRejected: 0,
            unitPrice: poItem.unitPrice,
            batchNumber: "",
            expiryDate: "",
            manufacturingDate: "",
            storageLocation: "",
            storageCondition: "",
            conditionOnReceipt: "good",
            damageNotes: "",
            qualityNotes: "",
            notes: poItem.notes || "",
            isSubstitution: false,
            originalProductId: "",
            substitutionReason: "",
            substitutionApprovedBy: "",
            substitutionApprovedAt: "",
            substitutionNotes: "",
          };
        });

        form.setValue("items", newItems);

        toast.success(
          `PO ${purchaseOrder.orderNumber} berhasil dimuat dengan ${purchaseOrder.items.length} item`,
        );
      }
    },
    [purchaseOrdersResponse, form],
  );

  const handleSubstitutionRequest = useCallback(
    (itemIndex: number, originalProductId: string) => {
      // Get product name for display
      const originalProductName = `Item ${itemIndex + 1}`;
      setSubstitutionDialog({
        isOpen: true,
        itemIndex,
        originalProductId,
        originalProductName,
      });
    },
    [],
  );

  const handleBatchValidationRequest = useCallback(
    (
      itemIndex: number,
      batchNumber: string,
      productId: string,
      productName: string,
    ) => {
      setBatchValidationDialog({
        isOpen: true,
        itemIndex,
        batchNumber,
        productId,
        productName,
      });
    },
    [],
  );

  const handleFormSubmit = async (values: GoodsReceiptFormValues) => {
    try {
      const submitData: CreateGoodsReceiptDto = {
        ...values,
        items: values.items.map((item) => ({
          ...item,
          totalPrice: item.quantityReceived * item.unitPrice,
        })),
      };

      await onSubmit(submitData);
    } catch (error) {
      console.error("Error submitting form:", error);
      toast.error("Gagal menyimpan penerimaan barang");
    }
  };

  return (
    <div className="flex flex-col h-full max-w-7xl mx-auto">
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(handleFormSubmit)}
          className="flex flex-col h-full"
        >
          {/* Header Section */}
          <div className="border-b">
            <FormHeaderSection
              form={form}
              purchaseOrdersResponse={purchaseOrdersResponse}
              onPurchaseOrderChange={handlePurchaseOrderChange}
              onClearPurchaseOrder={clearPurchaseOrderSelection}
            />
          </div>

          {/* Main Content */}
          <div className="flex-1 overflow-hidden">
            <Tabs
              value={activeTab}
              onValueChange={setActiveTab}
              className="h-full flex flex-col"
            >
              <div className="pt-4">
                <TabsList className="grid w-full grid-cols-2 lg:w-auto lg:grid-cols-2">
                  <TabsTrigger value="items" className="text-sm">
                    Item ({form.watch("items")?.length || 0})
                  </TabsTrigger>
                  <TabsTrigger value="details" className="text-sm">
                    Detail Tambahan
                  </TabsTrigger>
                </TabsList>
              </div>

              <TabsContent value="items" className="flex-1 overflow-hidden m-0">
                <ItemsSection
                  form={form}
                  selectedPurchaseOrder={selectedPurchaseOrder}
                  onSubstitutionRequest={handleSubstitutionRequest}
                  onBatchValidationRequest={handleBatchValidationRequest}
                />
              </TabsContent>

              <TabsContent value="details" className="flex-1 overflow-auto m-0">
                <DetailsSection form={form} />
              </TabsContent>
            </Tabs>
          </div>

          {/* Footer with Submit */}
          <div className="pt-4">
            <div className="flex items-center justify-between">
              <div className="text-muted-foreground">
                Total:{" "}
                <span className="font-medium text-foreground">
                  {formatCurrency(totalAmount)}
                </span>
              </div>
              <div className="flex gap-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => form.reset()}
                  disabled={isSubmitting}
                >
                  Reset
                </Button>
                <Button
                  type="submit"
                  disabled={isSubmitting || form.watch("items")?.length === 0}
                  className="min-w-[120px]"
                >
                  {isSubmitting
                    ? "Menyimpan..."
                    : mode === "edit"
                      ? "Update"
                      : "Simpan"}
                </Button>
              </div>
            </div>
          </div>
        </form>
      </Form>

      {/* Simple Substitution Dialog */}
      <SimpleSubstitutionDialog
        open={substitutionDialog.isOpen}
        onOpenChange={(open) => {
          if (!open) {
            setSubstitutionDialog({
              isOpen: false,
              itemIndex: -1,
              originalProductId: "",
              originalProductName: "",
            });
          }
        }}
        originalProductId={substitutionDialog.originalProductId}
        originalProductName={substitutionDialog.originalProductName}
        itemIndex={substitutionDialog.itemIndex}
        onSubstitutionConfirmed={(itemIndex, substitutionData) => {
          // Update form with simple substitution data
          form.setValue(`items.${itemIndex}.isSubstitution`, true);
          form.setValue(
            `items.${itemIndex}.productId`,
            substitutionData.productId,
          );
          form.setValue(
            `items.${itemIndex}.originalProductId`,
            substitutionData.originalProductId,
          );
          form.setValue(
            `items.${itemIndex}.substitutionReason`,
            substitutionData.substitutionReason,
          );
          form.setValue(
            `items.${itemIndex}.substitutionNotes`,
            substitutionData.substitutionNotes,
          );

          setSubstitutionDialog({
            isOpen: false,
            itemIndex: -1,
            originalProductId: "",
            originalProductName: "",
          });
        }}
      />

      {/* Batch Validation Dialog */}
      <BatchValidationDialog
        open={batchValidationDialog.isOpen}
        onOpenChange={(open) => {
          if (!open) {
            setBatchValidationDialog({
              isOpen: false,
              itemIndex: -1,
              batchNumber: "",
              productId: "",
              productName: "",
            });
          }
        }}
        batchNumber={batchValidationDialog.batchNumber}
        productId={batchValidationDialog.productId}
        productName={batchValidationDialog.productName}
        supplierId={form.watch("supplierId")}
        supplierName={selectedPurchaseOrder?.supplier?.name}
        onValidationComplete={(results) => {
          setBatchValidationResults((draft) => {
            draft[
              `${batchValidationDialog.productId}-${batchValidationDialog.batchNumber}`
            ] = results;
          });
        }}
      />
    </div>
  );
}

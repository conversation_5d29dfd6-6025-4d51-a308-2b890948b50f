"use client";

import { useState, useEffect } from "react";
import { useImmer } from "use-immer";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { Progress } from "@/components/ui/progress";
import {
  CheckCircle,
  XCircle,
  AlertTriangle,
  Clock,
  Shield,
  FileCheck,
  Zap,
  Loader2,
  RefreshCw,
  Info,
  ChevronRight,
  ChevronDown,
} from "lucide-react";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { cn } from "@/lib/utils";
import { useBatchValidation } from "@/hooks/use-batch-validation";
import { batchManagementApi } from "@/lib/api/batch-management";

interface BatchValidationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  batchNumber: string;
  productId: string;
  productName?: string;
  supplierId?: string;
  supplierName?: string;
  expiryDate?: Date;
  manufacturingDate?: Date;
  onValidationComplete?: (result: ValidationFlowResult) => void;
  autoStart?: boolean;
}

interface ValidationStep {
  id: string;
  label: string;
  description: string;
  status: "pending" | "running" | "success" | "warning" | "error";
  result?: any;
  message?: string;
  icon?: React.ReactNode;
  duration?: number;
}

interface ValidationFlowResult {
  overall: {
    isValid: boolean;
    level: "success" | "warning" | "error";
    message: string;
  };
  steps: ValidationStep[];
  auditTrail?: {
    validationId: string;
    timestamp: Date;
    userId: string;
  };
}

const VALIDATION_STEPS: Omit<
  ValidationStep,
  "status" | "result" | "message" | "duration"
>[] = [
  {
    id: "format",
    label: "Validasi Format",
    description: "Memeriksa format nomor batch sesuai standar farmasi",
    icon: <FileCheck className="h-4 w-4" />,
  },
  {
    id: "uniqueness",
    label: "Pemeriksaan Keunikan",
    description: "Memastikan batch number tidak duplikat dalam sistem",
    icon: <Zap className="h-4 w-4" />,
  },
  {
    id: "bpom",
    label: "Kepatuhan BPOM",
    description: "Verifikasi sesuai standar regulasi BPOM",
    icon: <Shield className="h-4 w-4" />,
  },
  {
    id: "expiry",
    label: "Penyelarasan Tanggal",
    description: "Validasi konsistensi tanggal produksi dan kadaluarsa",
    icon: <Clock className="h-4 w-4" />,
  },
];

export function BatchValidationDialog({
  open,
  onOpenChange,
  batchNumber,
  productId,
  productName,
  supplierId,
  supplierName,
  expiryDate,
  manufacturingDate,
  onValidationComplete,
  autoStart = true,
}: BatchValidationDialogProps) {
  const [steps, setSteps] = useImmer<ValidationStep[]>(
    VALIDATION_STEPS.map((step) => ({
      ...step,
      status: "pending" as const,
    })),
  );
  const [currentStepIndex, setCurrentStepIndex] = useState(-1);
  const [isValidating, setIsValidating] = useState(false);
  const [showDetails, setShowDetails] = useState(false);
  const [validationResult, setValidationResult] =
    useState<ValidationFlowResult | null>(null);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);

  const { validateComprehensive, checkUniqueness } = useBatchValidation(
    batchNumber,
    {
      productId,
      supplierId,
      expiryDate,
      manufacturingDate,
      enableRealTimeValidation: false,
    },
  );

  const progress =
    currentStepIndex >= 0 ? ((currentStepIndex + 1) / steps.length) * 100 : 0;

  // Auto-start validation when dialog opens
  useEffect(() => {
    if (open && autoStart && !isValidating && currentStepIndex === -1) {
      startValidation();
    }
  }, [open, autoStart]);

  const startValidation = async () => {
    if (!batchNumber || !productId) return;

    setIsValidating(true);
    setCurrentStepIndex(0);
    setValidationResult(null);

    // Reset all steps
    setSteps((draft) => {
      draft.forEach((step) => {
        step.status = "pending";
        step.result = undefined;
        step.message = undefined;
        step.duration = undefined;
      });
    });

    try {
      await runValidationFlow();
    } catch (error) {
      console.error("Validation flow error:", error);
      setSteps((draft) => {
        const currentStep = draft[currentStepIndex];
        if (currentStep) {
          currentStep.status = "error";
          currentStep.message = "Terjadi kesalahan saat validasi";
        }
      });
    } finally {
      setIsValidating(false);
    }
  };

  const runValidationFlow = async () => {
    const startTime = Date.now();

    // Step 1: Format Validation & Comprehensive Check
    await runStep(0, async () => {
      const stepStart = Date.now();
      const result = await validateComprehensive();

      setSteps((draft) => {
        const step = draft[0];
        step.duration = Date.now() - stepStart;
        step.result = result;

        if (result?.formatValidation) {
          const formatValid = result.formatValidation.failedRules.length === 0;
          step.status = formatValid ? "success" : "error";
          step.message = formatValid
            ? `Format valid: ${result.formatValidation.passedRules.join(", ")}`
            : `Format tidak valid: ${result.formatValidation.failedRules.join(", ")}`;
        } else {
          step.status = "error";
          step.message = "Gagal memvalidasi format";
        }
      });

      return result;
    });

    // Step 2: Uniqueness Check
    await runStep(1, async () => {
      const stepStart = Date.now();
      const result = await checkUniqueness();

      setSteps((draft) => {
        const step = draft[1];
        step.duration = Date.now() - stepStart;
        step.result = result;

        if (result) {
          step.status = result.isUnique ? "success" : "warning";
          step.message = result.isUnique
            ? "Batch number unik"
            : `Ditemukan ${result.conflicts.length} konflik batch`;
        } else {
          step.status = "error";
          step.message = "Gagal memeriksa keunikan";
        }
      });

      return result;
    });

    // Step 3: BPOM Compliance (using comprehensive result)
    await runStep(2, async () => {
      const stepStart = Date.now();
      const comprehensiveResult = steps[0].result;

      setSteps((draft) => {
        const step = draft[2];
        step.duration = Date.now() - stepStart;
        step.result = comprehensiveResult;

        if (comprehensiveResult?.bpomCompliant !== undefined) {
          step.status = comprehensiveResult.bpomCompliant
            ? "success"
            : "warning";
          step.message = comprehensiveResult.bpomCompliant
            ? `Sesuai standar BPOM (${comprehensiveResult.validationLevel})`
            : "Tidak sepenuhnya sesuai standar BPOM";
        } else {
          step.status = "warning";
          step.message = "Status kepatuhan BPOM tidak dapat ditentukan";
        }
      });

      return comprehensiveResult;
    });

    // Step 4: Expiry Date Alignment
    await runStep(3, async () => {
      const stepStart = Date.now();

      setSteps((draft) => {
        const step = draft[3];
        step.duration = Date.now() - stepStart;

        if (expiryDate && manufacturingDate) {
          const isValid = expiryDate > manufacturingDate;
          const daysDiff = Math.floor(
            (expiryDate.getTime() - manufacturingDate.getTime()) /
              (1000 * 60 * 60 * 24),
          );

          step.status = isValid ? "success" : "error";
          step.message = isValid
            ? `Tanggal valid (${daysDiff} hari shelf life)`
            : "Tanggal kadaluarsa harus setelah tanggal produksi";
          step.result = { isValid, daysDiff };
        } else if (expiryDate || manufacturingDate) {
          step.status = "warning";
          step.message = "Tidak semua tanggal tersedia untuk validasi";
          step.result = { isValid: true, daysDiff: null };
        } else {
          step.status = "success";
          step.message = "Tidak ada tanggal untuk divalidasi";
          step.result = { isValid: true, daysDiff: null };
        }
      });

      return steps[3].result;
    });

    // Generate final result
    const finalResult = generateValidationResult();
    setValidationResult(finalResult);

    if (onValidationComplete) {
      onValidationComplete(finalResult);
    }
  };

  const runStep = async (
    stepIndex: number,
    stepFunction: () => Promise<any>,
  ) => {
    setCurrentStepIndex(stepIndex);

    setSteps((draft) => {
      draft[stepIndex].status = "running";
    });

    await new Promise((resolve) => setTimeout(resolve, 300)); // Visual delay

    try {
      await stepFunction();
    } catch (error) {
      setSteps((draft) => {
        draft[stepIndex].status = "error";
        draft[stepIndex].message = "Terjadi kesalahan saat validasi";
      });
      throw error;
    }
  };

  const generateValidationResult = (): ValidationFlowResult => {
    const hasErrors = steps.some((step) => step.status === "error");
    const hasWarnings = steps.some((step) => step.status === "warning");

    let level: "success" | "warning" | "error";
    let message: string;

    if (hasErrors) {
      level = "error";
      message = "Validasi gagal. Terdapat error yang perlu diperbaiki.";
    } else if (hasWarnings) {
      level = "warning";
      message =
        "Validasi berhasil dengan peringatan. Silakan tinjau hasil validasi.";
    } else {
      level = "success";
      message = "Semua validasi berhasil. Batch number dapat digunakan.";
    }

    return {
      overall: {
        isValid: !hasErrors,
        level,
        message,
      },
      steps: [...steps],
      auditTrail: {
        validationId: `val_${Date.now()}`,
        timestamp: new Date(),
        userId: "current_user", // This should come from auth context
      },
    };
  };

  const getStepIcon = (step: ValidationStep) => {
    switch (step.status) {
      case "running":
        return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />;
      case "success":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "warning":
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case "error":
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return step.icon || <Clock className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStepColor = (step: ValidationStep) => {
    switch (step.status) {
      case "running":
        return "border-blue-200 bg-blue-50";
      case "success":
        return "border-green-200 bg-green-50";
      case "warning":
        return "border-yellow-200 bg-yellow-50";
      case "error":
        return "border-red-200 bg-red-50";
      default:
        return "border-gray-200 bg-gray-50";
    }
  };

  const handleClose = () => {
    if (isValidating) {
      setShowConfirmDialog(true);
    } else {
      onOpenChange(false);
      // Reset state when closing
      setCurrentStepIndex(-1);
      setValidationResult(null);
      setShowDetails(false);
    }
  };

  const handleForceClose = () => {
    setShowConfirmDialog(false);
    setIsValidating(false);
    onOpenChange(false);
    setCurrentStepIndex(-1);
    setValidationResult(null);
  };

  return (
    <>
      <Dialog open={open} onOpenChange={handleClose}>
        <DialogContent className="max-w-4xl max-h-[80vh]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              Validasi Batch Number - {batchNumber}
            </DialogTitle>
            <DialogDescription>
              Proses validasi komprehensif untuk batch number pada produk{" "}
              {productName || "yang dipilih"}
              {supplierName && ` dari supplier ${supplierName}`}
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-6">
            {/* Progress Bar */}
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Progress Validasi</span>
                <span>{Math.round(progress)}%</span>
              </div>
              <Progress value={progress} className="h-2" />
            </div>

            {/* Validation Steps */}
            <ScrollArea className="max-h-96">
              <div className="space-y-3">
                {steps.map((step, index) => (
                  <div
                    key={step.id}
                    className={cn(
                      "p-4 rounded-lg border transition-all duration-200",
                      getStepColor(step),
                      currentStepIndex === index &&
                        "ring-2 ring-blue-500 ring-opacity-30",
                    )}
                  >
                    <div className="flex items-start gap-3">
                      <div className="flex-shrink-0 mt-0.5">
                        {getStepIcon(step)}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <h4 className="font-medium text-sm">{step.label}</h4>
                          {step.duration && (
                            <span className="text-xs text-muted-foreground">
                              {step.duration}ms
                            </span>
                          )}
                        </div>
                        <p className="text-xs text-muted-foreground mt-1">
                          {step.description}
                        </p>
                        {step.message && (
                          <p
                            className={cn(
                              "text-xs mt-2 font-medium",
                              step.status === "success" && "text-green-700",
                              step.status === "warning" && "text-yellow-700",
                              step.status === "error" && "text-red-700",
                              step.status === "running" && "text-blue-700",
                            )}
                          >
                            {step.message}
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>

            {/* Validation Result Summary */}
            {validationResult && (
              <div className="border rounded-lg p-4 bg-muted/50">
                <h3 className="font-medium text-sm mb-3">Hasil Validasi</h3>
                <div className="flex items-center gap-2 mb-3">
                  {validationResult.overall.level === "success" && (
                    <CheckCircle className="h-5 w-5 text-green-500" />
                  )}
                  {validationResult.overall.level === "warning" && (
                    <AlertTriangle className="h-5 w-5 text-yellow-500" />
                  )}
                  {validationResult.overall.level === "error" && (
                    <XCircle className="h-5 w-5 text-red-500" />
                  )}
                  <Badge
                    variant={
                      validationResult.overall.level === "success"
                        ? "default"
                        : validationResult.overall.level === "warning"
                          ? "secondary"
                          : "destructive"
                    }
                  >
                    {validationResult.overall.level === "success" && "VALID"}
                    {validationResult.overall.level === "warning" &&
                      "VALID (PERINGATAN)"}
                    {validationResult.overall.level === "error" &&
                      "TIDAK VALID"}
                  </Badge>
                </div>
                <p className="text-sm text-muted-foreground">
                  {validationResult.overall.message}
                </p>

                {/* Expandable Details */}
                <Collapsible open={showDetails} onOpenChange={setShowDetails}>
                  <CollapsibleTrigger asChild>
                    <Button variant="ghost" size="sm" className="mt-3 h-8 p-0">
                      <span className="text-xs">Detail Validasi</span>
                      {showDetails ? (
                        <ChevronDown className="h-3 w-3 ml-1" />
                      ) : (
                        <ChevronRight className="h-3 w-3 ml-1" />
                      )}
                    </Button>
                  </CollapsibleTrigger>
                  <CollapsibleContent className="mt-3">
                    <Separator className="mb-3" />
                    <div className="space-y-2 text-xs">
                      {validationResult.steps.map((step) => (
                        <div key={step.id} className="flex justify-between">
                          <span>{step.label}:</span>
                          <span
                            className={cn(
                              "font-medium",
                              step.status === "success" && "text-green-600",
                              step.status === "warning" && "text-yellow-600",
                              step.status === "error" && "text-red-600",
                            )}
                          >
                            {step.status === "success" && "✓ Berhasil"}
                            {step.status === "warning" && "⚠ Peringatan"}
                            {step.status === "error" && "✗ Error"}
                          </span>
                        </div>
                      ))}
                    </div>
                  </CollapsibleContent>
                </Collapsible>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex justify-between">
              <div className="flex gap-2">
                {!isValidating && currentStepIndex === -1 && (
                  <Button onClick={startValidation} size="sm">
                    Mulai Validasi
                  </Button>
                )}
                {!isValidating && validationResult && (
                  <Button onClick={startValidation} variant="outline" size="sm">
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Validasi Ulang
                  </Button>
                )}
              </div>
              <div className="flex gap-2">
                <Button variant="outline" onClick={handleClose} size="sm">
                  {isValidating ? "Batal" : "Tutup"}
                </Button>
                {validationResult && (
                  <Button
                    onClick={() => onOpenChange(false)}
                    size="sm"
                    disabled={validationResult.overall.level === "error"}
                  >
                    {validationResult.overall.level === "error"
                      ? "Perbaiki Error"
                      : "Lanjutkan"}
                  </Button>
                )}
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Confirmation Dialog for Canceling Validation */}
      <AlertDialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Batalkan Validasi?</AlertDialogTitle>
            <AlertDialogDescription>
              Proses validasi batch number sedang berjalan. Apakah Anda yakin
              ingin membatalkannya?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Tidak</AlertDialogCancel>
            <AlertDialogAction onClick={handleForceClose}>
              Ya, Batalkan
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}

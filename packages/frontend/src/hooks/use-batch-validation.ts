"use client";

import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useDebounce, DEBOUNCE_DELAYS } from "@/lib/utils/debounce";
import { batchManagementApi } from "@/lib/api/batch-management";
import { toast } from "sonner";
import { useCallback } from "react";

// Types are now imported from the API client or defined there

// Query key factory
export const batchValidationKeys = {
  all: ['batch-validation'] as const,
  realTime: () => [...batchValidationKeys.all, 'real-time'] as const,
  realTimeValidation: (batchNumber: string, productId: string) =>
    [...batchValidationKeys.realTime(), batchNumber, productId] as const,
  rules: () => [...batchValidationKeys.all, 'rules'] as const,
  history: () => [...batchValidationKeys.all, 'history'] as const,
  historyDetail: (batchNumber: string) =>
    [...batchValidationKeys.history(), batchNumber] as const,
};

interface UseBatchValidationOptions {
  productId?: string;
  supplierId?: string;
  expiryDate?: Date;
  manufacturingDate?: Date;
  debounceMs?: number;
  enableRealTimeValidation?: boolean;
}

// Real-time batch validation hook
export function useBatchValidationRealTime(
  batchNumber: string,
  productId: string,
  options: { debounceMs?: number; enabled?: boolean } = {},
) {
  const { debounceMs = DEBOUNCE_DELAYS.VALIDATION, enabled = true } = options;
  const debouncedBatchNumber = useDebounce(batchNumber, debounceMs);

  return useQuery({
    queryKey: batchValidationKeys.realTimeValidation(debouncedBatchNumber, productId),
    queryFn: () => batchManagementApi.validateBatchNumberRealTime(debouncedBatchNumber, productId),
    enabled: enabled && !!debouncedBatchNumber && !!productId,
    staleTime: 30000, // 30 seconds
    retry: (failureCount, error) => {
      // Don't retry on 4xx errors (client errors)
      if (error instanceof Error && error.message.includes('HTTP 4')) {
        return false;
      }
      return failureCount < 2;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
}

// Legacy hook for backward compatibility - now uses the new pattern
export function useBatchValidation(
  batchNumber: string,
  options: UseBatchValidationOptions = {},
) {
  const {
    productId,
    debounceMs = DEBOUNCE_DELAYS.VALIDATION,
    enableRealTimeValidation = true,
  } = options;

  const realTimeQuery = useBatchValidationRealTime(
    batchNumber,
    productId || '',
    { debounceMs, enabled: enableRealTimeValidation }
  );

  // Format validation helper
  const getFormatSuggestion = useCallback(
    (batchNumber: string): string | null => {
      if (!batchNumber) return null;

      const patterns = {
        CONTROLLED: /^[A-Z]{3}[0-9]{8}[A-Z]{2}$/,
        BPOM_COMPLIANT: /^[A-Z]{2,4}[0-9]{6}[A-Z0-9]{2,6}$/,
        STANDARD: /^[A-Z]{2,4}[0-9]{4,8}[A-Z0-9]{0,8}$/,
        GENERIC: /^GEN[0-9]{6}[A-Z0-9]{2,4}$/,
        IMPORT: /^IMP[A-Z]{2,3}[A-Z0-9]{4,12}$/,
        BASIC: /^[A-Za-z0-9\-_]{3,20}$/,
      };

      for (const [format, pattern] of Object.entries(patterns)) {
        if (pattern.test(batchNumber.toUpperCase())) {
          return format;
        }
      }

      return null;
    },
    [],
  );



  return {
    // Validation state
    validationResult: realTimeQuery.data,
    isValid: realTimeQuery.data?.isValid ?? false,
    hasWarnings: realTimeQuery.data?.level === "warning",
    hasErrors: realTimeQuery.data?.level === "error",
    isLoading: realTimeQuery.isLoading || realTimeQuery.isFetching,

    // Helpers
    getFormatSuggestion,

    // Raw data
    realTimeResult: realTimeQuery.data,
    realTimeError: realTimeQuery.error,

    // Legacy methods for backward compatibility
    validateComprehensive: useBatchValidationComprehensive(),
    checkUniqueness: useBatchUniquenessCheck(),
  };
}

// Hook for batch validation rules
export function useBatchValidationRules() {
  return useQuery({
    queryKey: batchValidationKeys.rules(),
    queryFn: () => batchManagementApi.getValidationRules(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}

// Hook for batch history with caching
export function useBatchHistory(batchNumber: string) {
  return useQuery({
    queryKey: batchValidationKeys.historyDetail(batchNumber),
    queryFn: () => {
      if (!batchNumber) return null;
      return batchManagementApi.getBatchHistoryByNumber(batchNumber);
    },
    enabled: !!batchNumber,
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook for comprehensive batch validation
export function useBatchValidationComprehensive() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: {
      batchNumber: string;
      productId: string;
      supplierId?: string;
      expiryDate?: string;
      manufacturingDate?: string;
    }) => batchManagementApi.validateBatchNumber(data),
    onSuccess: () => {
      // Invalidate real-time validation queries
      queryClient.invalidateQueries({ queryKey: batchValidationKeys.realTime() });
      toast.success('Validasi batch berhasil');
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || 'Gagal melakukan validasi batch');
    },
  });
}

// Hook for batch uniqueness check
export function useBatchUniquenessCheck() {
  return useMutation({
    mutationFn: (data: {
      batchNumber: string;
      productId: string;
      supplierId?: string;
    }) => batchManagementApi.checkBatchUniqueness(data.batchNumber, data.productId, data.supplierId),
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || 'Gagal memeriksa keunikan batch');
    },
  });
}
